import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CartService } from '../../../core/services/cart.service';
import { GameService } from '../../../core/services/game.service';
import { CheckoutService } from '../../../core/services/checkout.service';
import { UserSummaryService } from '../../../core/services/user-summary.service';
import { ModalService } from '../../../core/services/modal.service';
import { Cart, CartItem } from '../../../core/models/cart.model';
import { Game } from '../../../core/models/game.model';
import { Purchase } from '../../../core/models/cart.model';

@Component({
  selector: 'app-profile-cart',
  standalone: false,
  templateUrl: './profile-cart.component.html',
  styleUrl: './profile-cart.component.css'
})
export class ProfileCartComponent implements OnInit, OnDestroy {
  cart: Cart = { items: [], total_items: 0, total_price: 0 };
  loading = false;
  error = '';
  checkoutLoading = false;

  // Game data cache
  gameDataCache: { [gameId: number]: Game } = {};
  loadingGameData = false;

  private cartSubscription?: Subscription;

  constructor(
    private cartService: CartService,
    private gameService: GameService,
    private checkoutService: CheckoutService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCart();
    this.setupCartSubscription();
  }

  ngOnDestroy(): void {
    this.cartSubscription?.unsubscribe();
  }

  private setupCartSubscription(): void {
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cart = cart;
      this.loadGameDataForCartItems();
    });
  }

  loadCart(): void {
    this.loading = true;
    this.error = '';

    this.cartService.loadCart().subscribe({
      next: (cart) => {
        this.cart = cart;
        this.loading = false;
        this.loadGameDataForCartItems();
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить корзину';
        this.loading = false;
      }
    });
  }

  loadGameDataForCartItems(): void {
    if (this.cart.items.length === 0) return;

    const gameIds = this.cart.items
      .map(item => item.game)
      .filter((gameId): gameId is number => gameId !== undefined && !this.gameDataCache[gameId]);

    if (gameIds.length === 0) return;

    this.loadingGameData = true;

    // Load game data for each game ID not in cache
    gameIds.forEach(gameId => {
      this.gameService.getGame(gameId).subscribe({
        next: (game: Game) => {
          this.gameDataCache[gameId] = game;
          this.loadingGameData = false;
        },
        error: (error: any) => {
          console.error(`Error loading game data for ID ${gameId}:`, error);
          this.loadingGameData = false;
        }
      });
    });
  }

  getGameData(gameId: number | undefined): Game | undefined {
    if (!gameId) return undefined;
    return this.gameDataCache[gameId];
  }



  removeItem(item: CartItem): void {
    this.modalService.confirm(
      'Удалить из корзины',
      `Вы уверены, что хотите удалить "${item.game_title}" из корзины?`
    ).then((confirmed) => {
      if (confirmed) {
        this.cartService.removeFromCart(item.id).subscribe({
          next: () => {
            console.log('Item removed successfully');
          },
          error: (error) => {
            console.error('Error removing item:', error);
            this.modalService.error('Ошибка', 'Не удалось удалить товар: ' + error.message);
          }
        });
      }
    });
  }

  clearCart(): void {
    if (this.cart.items.length === 0) return;

    this.modalService.confirm(
      'Очистить корзину',
      'Вы уверены, что хотите удалить все товары из корзины?'
    ).then((confirmed) => {
      if (confirmed) {
        this.cartService.clearCart().subscribe({
          next: () => {
            console.log('Cart cleared successfully');
          },
          error: (error) => {
            console.error('Error clearing cart:', error);
            this.modalService.error('Ошибка', 'Не удалось очистить корзину: ' + error.message);
          }
        });
      }
    });
  }

  checkout(): void {
    if (this.cart.items.length === 0) return;

    this.checkoutLoading = true;

    this.checkoutService.checkout().subscribe({
      next: (purchases: Purchase[]) => {
        this.checkoutLoading = false;

        // Refresh user summary to update cart count (cart should be cleared after checkout)
        this.userSummaryService.refreshSummary();

        // Show success message
        this.modalService.success(
          'Заказ оформлен',
          `Ваш заказ на сумму ${this.cart.total_price}₽ успешно оформлен! Сейчас вы будете перенаправлены на страницу оплаты.`
        ).then(() => {
          // Navigate directly to purchases section for payment
          this.router.navigate(['/profile/purchases']);
        });
      },
      error: (error) => {
        this.checkoutLoading = false;
        console.error('Checkout error:', error);
        this.modalService.error('Ошибка оформления заказа', error.message || 'Не удалось оформить заказ');
      }
    });
  }

  goToGames(): void {
    this.router.navigate(['/profile'], { fragment: 'catalog' });
  }

  viewGameDetails(gameId: number | undefined): void {
    if (gameId) {
      this.router.navigate(['/games', gameId]);
    }
  }

  getItemTotal(item: CartItem): number {
    if (item.game) {
      const gameData = this.getGameData(item.game);
      if (!gameData) return 0;
      const price = parseFloat(gameData.price) || 0;
      return price;
    } else if (item.package_data) {
      const price = parseFloat(item.package_data.price) || 0;
      return price * item.quantity;
    }
    return 0;
  }
}
