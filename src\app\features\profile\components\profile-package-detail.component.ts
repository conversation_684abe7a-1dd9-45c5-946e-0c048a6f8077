import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { GamePackageService } from '../../../core/services/game-package.service';
import { CartService } from '../../../core/services/cart.service';
import { AuthService } from '../../../core/services/auth.service';
import { ModalService } from '../../../core/services/modal.service';
import { GamePackage } from '../../../core/models/game-package.model';

@Component({
  selector: 'app-profile-package-detail',
  standalone: false,
  templateUrl: './profile-package-detail.component.html',
  styleUrl: './profile-package-detail.component.css'
})
export class ProfilePackageDetailComponent implements OnInit {
  package: GamePackage | null = null;
  packageLoading = false;
  packageError = '';
  packageId: number | null = null;

  // Cart operations
  addingToCart = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private packageService: GamePackageService,
    private cartService: CartService,
    private authService: AuthService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const id = parseInt(params['id']);
      if (id) {
        this.packageId = id;
        this.loadPackage(id);
      } else {
        this.packageError = 'Invalid package ID';
      }
    });
  }

  loadPackage(id: number): void {
    this.packageLoading = true;
    this.packageError = '';

    this.packageService.getGamePackage(id).subscribe({
      next: (packageData) => {
        this.package = packageData;
        this.packageLoading = false;
      },
      error: (error) => {
        this.packageError = error.message || 'Failed to load package';
        this.packageLoading = false;
      }
    });
  }

  /**
   * Add package to cart with quantity
   */
  addToCart(quantity: number = 1): void {
    if (!this.package) return;

    if (!this.authService.isAuthenticated()) {
      this.modalService.error('Вход в систему', 'Для добавления в корзину необходимо войти в систему');
      return;
    }

    this.addingToCart = true;

    this.cartService.addPackageToCart(this.package, quantity).subscribe({
      next: () => {
        this.addingToCart = false;
        this.modalService.success('Успех', 'Пакет добавлен в корзину');
      },
      error: (error) => {
        this.addingToCart = false;
        console.error('Error adding package to cart:', error);
        this.modalService.error('Ошибка', 'Ошибка при добавлении пакета в корзину');
      }
    });
  }

  /**
   * Navigate back to packages catalog
   */
  goBack(): void {
    this.router.navigate(['/profile/packages']);
  }

  /**
   * Navigate to cart
   */
  goToCart(): void {
    this.router.navigate(['/profile/cart']);
  }

  /**
   * Format price for display
   */
  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  /**
   * Calculate price per day
   */
  getPricePerDay(): string {
    if (!this.package) return '0';
    const totalPrice = parseFloat(this.package.price);
    const pricePerDay = totalPrice / this.package.duration_days;
    return pricePerDay.toLocaleString('ru-RU', { maximumFractionDigits: 0 });
  }
}
