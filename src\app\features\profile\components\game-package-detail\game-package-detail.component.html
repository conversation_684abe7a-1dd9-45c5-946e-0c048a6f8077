<!-- Modal Backdrop -->
<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" (click)="closeModal()">
  <!-- Modal Content -->
  <div class="bg-slate-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto" (click)="onModalContentClick($event)">
    <!-- Modal Header -->
    <div class="flex items-center justify-between p-6 border-b border-slate-700">
      <h2 class="text-xl font-bold text-white">Package Details</h2>
      <button
        (click)="closeModal()"
        class="text-gray-400 hover:text-white transition-colors"
      >
        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Loading State -->
    <div *ngIf="packageLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error State -->
    <div *ngIf="packageError && !packageLoading" class="p-6">
      <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-4">
        <p class="text-red-400">{{ packageError }}</p>
      </div>
    </div>

    <!-- Package Content -->
    <div *ngIf="package && !packageLoading" class="p-6">
      <!-- Action Buttons -->
      <div class="flex gap-3 mb-6">
        <button
          *ngIf="!isEditing"
          (click)="toggleEditMode()"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit Package
        </button>

        <button
          *ngIf="!isEditing"
          (click)="deletePackage()"
          class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          Delete Package
        </button>
      </div>

      <!-- View Mode -->
      <div *ngIf="!isEditing">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Basic Information -->
          <div class="space-y-4">
            <div>
              <h3 class="text-lg font-semibold text-white mb-2">{{ package.name }}</h3>
              <p class="text-gray-300">{{ package.description }}</p>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-gray-400 text-sm font-medium mb-1">Price</label>
                <p class="text-white font-semibold">{{ package.price }}₸</p>
              </div>
              <div>
                <label class="block text-gray-400 text-sm font-medium mb-1">Duration</label>
                <p class="text-white">{{ package.duration_days }} days</p>
              </div>
            </div>

            <div>
              <label class="block text-gray-400 text-sm font-medium mb-1">Max Selectable Games</label>
              <p class="text-white">{{ package.max_selectable_games }}</p>
            </div>

            <!-- Benefits -->
            <div *ngIf="package.benefit_1 || package.benefit_2 || package.benefit_3">
              <label class="block text-gray-400 text-sm font-medium mb-2">Benefits</label>
              <ul class="space-y-1">
                <li *ngIf="package.benefit_1" class="text-gray-300 text-sm flex items-center gap-2">
                  <svg class="h-4 w-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ package.benefit_1 }}
                </li>
                <li *ngIf="package.benefit_2" class="text-gray-300 text-sm flex items-center gap-2">
                  <svg class="h-4 w-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ package.benefit_2 }}
                </li>
                <li *ngIf="package.benefit_3" class="text-gray-300 text-sm flex items-center gap-2">
                  <svg class="h-4 w-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ package.benefit_3 }}
                </li>
              </ul>
            </div>
          </div>

          <!-- Games -->
          <div>
            <label class="block text-gray-400 text-sm font-medium mb-2">Included Games ({{ package.games.length }})</label>
            <div class="space-y-2 max-h-64 overflow-y-auto">
              <div *ngFor="let game of package.games" 
                   class="bg-slate-700/50 rounded-lg p-3 flex items-center justify-between">
                <span class="text-white text-sm">{{ game.title }}</span>
                <span class="text-xs text-gray-400">ID: {{ game.id }}</span>
              </div>
            </div>
            <div *ngIf="package.games.length === 0" class="text-gray-400 text-sm italic">
              No games selected for this package
            </div>
          </div>
        </div>
      </div>

      <!-- Edit Mode -->
      <div *ngIf="isEditing">
        <form (ngSubmit)="savePackage()" class="space-y-6">
          <!-- Error Message -->
          <div *ngIf="editPackageError" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
            <p class="text-red-400 text-sm">{{ editPackageError }}</p>
          </div>

          <!-- Basic Info -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Package Name *</label>
              <input
                type="text"
                [(ngModel)]="editPackage.name"
                name="name"
                required
                class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter package name"
              >
            </div>

            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Price *</label>
              <input
                type="text"
                [(ngModel)]="editPackage.price"
                name="price"
                required
                class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0.00"
              >
            </div>
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Description *</label>
            <textarea
              [(ngModel)]="editPackage.description"
              name="description"
              required
              rows="3"
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="Enter package description"
            ></textarea>
          </div>

          <!-- Benefits -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Benefit 1</label>
              <input
                type="text"
                [(ngModel)]="editPackage.benefit_1"
                name="benefit_1"
                class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="First benefit"
              >
            </div>

            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Benefit 2</label>
              <input
                type="text"
                [(ngModel)]="editPackage.benefit_2"
                name="benefit_2"
                class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Second benefit"
              >
            </div>

            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Benefit 3</label>
              <input
                type="text"
                [(ngModel)]="editPackage.benefit_3"
                name="benefit_3"
                class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Third benefit"
              >
            </div>
          </div>

          <!-- Duration and Max Games -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Duration (Days) *</label>
              <input
                type="text"
                [(ngModel)]="editPackage.duration_days"
                name="duration_days"
                required
                class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="30"
              >
            </div>

            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Max Selectable Games *</label>
              <input
                type="text"
                [(ngModel)]="editPackage.max_selectable_games"
                name="max_selectable_games"
                required
                class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="2"
              >
            </div>
          </div>

          <!-- Game Selection -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Select Games</label>
            <div *ngIf="gamesLoading" class="text-gray-400 text-sm">Loading games...</div>
            <div *ngIf="!gamesLoading" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 max-h-48 overflow-y-auto bg-slate-700/30 rounded-lg p-3">
              <label *ngFor="let game of availableGames" class="flex items-center space-x-2 cursor-pointer hover:bg-slate-600/30 rounded p-2">
                <input
                  type="checkbox"
                  [checked]="isGameSelected(game.id)"
                  (change)="onGameSelectionChange(game.id, $event)"
                  class="rounded border-slate-500 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
                >
                <span class="text-white text-sm">{{ game.title }}</span>
              </label>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex gap-3 pt-4 border-t border-slate-700">
            <button
              type="submit"
              [disabled]="editPackageLoading"
              class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
            >
              <div *ngIf="editPackageLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {{ editPackageLoading ? 'Saving...' : 'Save Changes' }}
            </button>

            <button
              type="button"
              (click)="toggleEditMode()"
              class="bg-slate-600 hover:bg-slate-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
