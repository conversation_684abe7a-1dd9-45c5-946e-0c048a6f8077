import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { GameService } from '../../core/services/game.service';
import { GamePackageService } from '../../core/services/game-package.service';
import { CartService } from '../../core/services/cart.service';
import { AuthService } from '../../core/services/auth.service';
import { ModalService } from '../../core/services/modal.service';
import { Game } from '../../core/models/game.model';
import { GamePackage } from '../../core/models/game-package.model';

interface FaqItem {
  id: number;
  question: string;
  answer: string;
  isOpen: boolean;
}

@Component({
  selector: 'app-main',
  standalone: false,
  templateUrl: './main.html',
  styleUrl: './main.css'
})
export class Main implements OnInit {
  // Featured games for main page (limit to 3)
  featuredGames: Game[] = [];
  gamesLoading = false;
  gamesError = '';

  // Featured packages for main page (limit to 3)
  featuredPackages: GamePackage[] = [];
  packagesLoading = false;
  packagesError = '';

  // FAQ data
  faqItems: FaqItem[] = [
    {
      id: 1,
      question: 'Какое оборудование нужно для игр?',
      answer: 'Для большинства игр достаточно ноутбука или ПК с микрофонами. Некоторые игры могут требовать геймпады или дополнительные устройства.',
      isOpen: true // First item is open by default
    },
    {
      id: 2,
      question: 'Сколько человек может играть одновременно?',
      answer: 'Количество игроков зависит от конкретной игры. Обычно от 2 до 8 человек могут участвовать одновременно.',
      isOpen: false
    },
    {
      id: 3,
      question: 'Как долго действует ключ активации?',
      answer: 'Ключ активации действует 10 часов с момента первого запуска игры. В течение этого времени можно играть неограниченно.',
      isOpen: false
    }
  ];

  constructor(
    private gameService: GameService,
    private gamePackageService: GamePackageService,
    private cartService: CartService,
    public authService: AuthService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadFeaturedGames();
    this.loadFeaturedPackages();
  }

  /**
   * Load featured games for the main page (first 3 games)
   */
  loadFeaturedGames(): void {
    this.gamesLoading = true;
    this.gamesError = '';

    this.gameService.getGames(undefined, 1, 3).subscribe({
      next: (response) => {
        this.featuredGames = response.results.slice(0, 3);
        this.gamesLoading = false;
      },
      error: (error) => {
        console.error('Error loading featured games:', error);
        this.gamesError = 'Ошибка загрузки игр';
        this.gamesLoading = false;
      }
    });
  }

  /**
   * Load featured packages for the main page (first 3 packages)
   */
  loadFeaturedPackages(): void {
    this.packagesLoading = true;
    this.packagesError = '';

    this.gamePackageService.getGamePackages({}, 1, 3).subscribe({
      next: (response) => {
        this.featuredPackages = response.results;
        this.packagesLoading = false;
      },
      error: (error) => {
        console.error('Error loading packages:', error);
        this.packagesError = 'Ошибка загрузки пакетов';
        this.packagesLoading = false;
      }
    });
  }

  /**
   * Navigate to games catalog page
   */
  viewAllGames(): void {
    this.router.navigate(['/games']);
  }

  /**
   * Navigate to games based on authentication status
   */
  navigateToGames(): void {
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/profile/games']);
    } else {
      this.router.navigate(['/login']);
    }
  }

  /**
   * Navigate to all games based on authentication status
   */
  navigateToAllGames(event: Event): void {
    event.preventDefault();
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/profile/games']);
    } else {
      this.router.navigate(['/login']);
    }
  }

  /**
   * Navigate to game detail page
   */
  viewGameDetails(gameId: number): void {
    this.router.navigate(['/games', gameId]);
  }

  /**
   * Smooth scroll to section with proper offset for fixed header
   */
  scrollToSection(sectionId: string): void {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 80; // Approximate header height
      const elementPosition = element.offsetTop - headerHeight;

      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
    }
  }

  /**
   * Scroll to top of page
   */
  scrollToTop(): void {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  /**
   * Add game to cart
   */
  addToCart(game: Game, event: Event): void {
    event.stopPropagation(); // Prevent navigation to game details

    if (!this.authService.isAuthenticated()) {
      this.modalService.error('Вход в систему', 'Для добавления в корзину необходимо войти в систему');
      return;
    }

    // Allow adding to cart if game is in library but access has expired (for access extension)
    if (this.isInLibrary(game) && this.hasActiveAccess(game)) {
      this.modalService.error('Игра уже в библиотеке', 'У вас уже есть активный доступ к этой игре');
      return;
    }

    if (this.isInCart(game)) {
      return;
    }

    this.cartService.addToCart(game).subscribe({
      next: () => {
        // Update the local game object to reflect the cart status immediately
        game.is_in_cart = true;
        this.modalService.success('Успех', 'Игра добавлена в корзину');
      },
      error: (error) => {
        console.error('Error adding to cart:', error);
        this.modalService.error('Ошибка', 'Ошибка при добавлении в корзину');
      }
    });
  }

  /**
   * Check if game is already in cart
   */
  isInCart(game: Game): boolean {
    return game.is_in_cart || false;
  }

  /**
   * Check if game is already in library
   */
  isInLibrary(game: Game): boolean {
    return game.is_in_library || false;
  }

  /**
   * Format price for display
   */
  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return new Intl.NumberFormat('ru-KZ', {
      style: 'currency',
      currency: 'KZT',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numPrice);
  }

  hasActiveAccess(game: Game): boolean {
    return game.has_access || false;
  }

  hasUnactivatedAccess(game: Game): boolean {
    return game.has_unactivated_access || false;
  }

  canPlay(game: Game): boolean {
    return this.isInLibrary(game) && this.hasActiveAccess(game);
  }

  needsAccessExtension(game: Game): boolean {
    return this.isInLibrary(game) && !this.hasActiveAccess(game) && !this.hasUnactivatedAccess(game);
  }

  canBuy(game: Game): boolean {
    return !this.isInLibrary(game);
  }

  getRemainingTime(game: Game): string {
    if (!game.access_end) return '';
    const accessEnd = new Date(game.access_end);
    const now = new Date();
    const diff = accessEnd.getTime() - now.getTime();

    if (diff <= 0) return 'Истёк';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) {
      return `${days}д`;
    } else if (hours > 0) {
      return `${hours}ч`;
    } else {
      return '<1ч';
    }
  }

  /**
   * Add package to cart
   */
  addPackageToCart(gamePackage: GamePackage): void {
    if (!this.authService.isAuthenticated()) {
      this.modalService.error('Вход в систему', 'Для добавления в корзину необходимо войти в систему');
      return;
    }

    this.cartService.addPackageToCart(gamePackage).subscribe({
      next: () => {
        this.modalService.success('Успех', 'Пакет добавлен в корзину');
      },
      error: (error) => {
        console.error('Error adding package to cart:', error);
        this.modalService.error('Ошибка', 'Ошибка при добавлении пакета в корзину');
      }
    });
  }

  /**
   * Navigate to package details
   */
  viewPackageDetails(gamePackage: GamePackage): void {
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/profile/packages', gamePackage.id]);
    } else {
      this.router.navigate(['/login']);
    }
  }

  /**
   * Navigate to packages catalog
   */
  viewAllPackages(): void {
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/profile/packages']);
    } else {
      this.router.navigate(['/login']);
    }
  }

  /**
   * Toggle FAQ item open/closed state
   */
  toggleFaq(faqId: number): void {
    const faqItem = this.faqItems.find(item => item.id === faqId);
    if (faqItem) {
      faqItem.isOpen = !faqItem.isOpen;
    }
  }
}
