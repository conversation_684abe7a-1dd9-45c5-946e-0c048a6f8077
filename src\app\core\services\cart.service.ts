import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError, forkJoin, Subject } from 'rxjs';
import { catchError, map, tap, switchMap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { Cart, CartItem, AddToCartRequest, ApiCartResponse, CartError } from '../models/cart.model';
import { Game } from '../models/game.model';
import { GamePackage } from '../models/game-package.model';
import { UserSummaryService } from './user-summary.service';
import { GameService } from './game.service';
import { GamePackageService } from './game-package.service';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class CartService {
  private apiUrl = `${environment.apiUrl}/api/cart`;
  private cartSubject = new BehaviorSubject<Cart>({ items: [], total_items: 0, total_price: 0 });

  // Subject to notify about cart changes with specific game IDs
  private cartChangeSubject = new Subject<{ action: 'added' | 'removed', gameId: number }>();

  constructor(
    private http: HttpClient,
    private gameService: GameService,
    private gamePackageService: GamePackageService,
    private authService: AuthService,
    private userSummaryService: UserSummaryService
  ) {
    // Subscribe to authentication changes and load/clear cart accordingly
    this.authService.isAuthenticated$.subscribe(isAuthenticated => {
      if (isAuthenticated) {
        // User is authenticated, load cart
        this.loadCart().subscribe({
          error: (error) => {
            console.error('Failed to load cart:', error);
            // Reset cart to empty state on error
            this.cartSubject.next({ items: [], total_items: 0, total_price: 0 });
          }
        });
      } else {
        // User is not authenticated, clear cart
        this.cartSubject.next({ items: [], total_items: 0, total_price: 0 });
      }
    });
  }

  get cart$(): Observable<Cart> {
    return this.cartSubject.asObservable();
  }

  get currentCart(): Cart {
    return this.cartSubject.value;
  }

  get cartChanges$(): Observable<{ action: 'added' | 'removed', gameId: number }> {
    return this.cartChangeSubject.asObservable();
  }

  /**
   * Refresh cart data (useful for manual refresh)
   */
  refreshCart(): void {
    if (this.authService.isAuthenticated()) {
      this.loadCart().subscribe({
        error: (error) => {
          console.error('Failed to refresh cart:', error);
        }
      });
    }
  }

  /**
   * Load cart from API
   */
  loadCart(): Observable<Cart> {
    return this.http.get<ApiCartResponse>(`${this.apiUrl}/`).pipe(
      map(response => this.enrichCartItems(response.results)),
      tap(cart => this.cartSubject.next(cart)),
      catchError(error => {
        // For authentication errors, just return empty cart instead of throwing
        if (error.status === 401) {
          const emptyCart = { items: [], total_items: 0, total_price: 0 };
          this.cartSubject.next(emptyCart);
          return [emptyCart];
        }
        return this.handleError(error);
      })
    );
  }

  /**
   * Add game to cart
   */
  addToCart(game: Game): Observable<CartItem> {
    const request: AddToCartRequest = {
      game: game.id,
      quantity: 1
    };

    return this.http.post<any>(`${this.apiUrl}/`, request).pipe(
      tap(() => {
        // Reload cart after successful addition
        this.loadCart().subscribe();
        // Refresh user summary to update cart count
        this.userSummaryService.refreshSummary();
        // Notify about the cart change
        this.cartChangeSubject.next({ action: 'added', gameId: game.id });
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Add package to cart
   */
  addPackageToCart(gamePackage: GamePackage, quantity: number = 1): Observable<CartItem> {
    const request: AddToCartRequest = {
      game_package: gamePackage.id,
      quantity: quantity
    };

    return this.http.post<any>(`${this.apiUrl}/`, request).pipe(
      tap(() => {
        // Reload cart after successful addition
        this.loadCart().subscribe();
        // Refresh user summary to update cart count
        this.userSummaryService.refreshSummary();
        // Notify about the cart change (using package ID as gameId for now)
        this.cartChangeSubject.next({ action: 'added', gameId: gamePackage.id });
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Remove item from cart
   */
  removeFromCart(cartItemId: number): Observable<void> {
    // Find the cart item to get the game ID before removing
    const cartItem = this.currentCart.items.find(item => item.id === cartItemId);
    const gameId = cartItem?.game;

    return this.http.delete<void>(`${this.apiUrl}/${cartItemId}/`).pipe(
      tap(() => {
        // Reload cart after successful removal
        this.loadCart().subscribe();
        // Refresh user summary to update cart count
        this.userSummaryService.refreshSummary();
        // Notify about the cart change if we have the game ID
        if (gameId) {
          this.cartChangeSubject.next({ action: 'removed', gameId });
        }
      }),
      catchError(this.handleError)
    );
  }



  /**
   * Clear entire cart (remove all items)
   */
  clearCart(): Observable<void> {
    const currentCart = this.currentCart;
    const gameIds = currentCart.items.map(item => item.game); // Store game IDs before clearing
    const deleteRequests = currentCart.items.map(item =>
      this.http.delete<void>(`${this.apiUrl}/${item.id}/`)
    );

    return forkJoin(deleteRequests).pipe(
      map(() => void 0),
      tap(() => {
        // Reload cart after clearing
        this.loadCart().subscribe();
        // Refresh user summary to update cart count
        this.userSummaryService.refreshSummary();
        // Notify about all removed games
        gameIds.forEach(gameId => {
          if (gameId) {
            this.cartChangeSubject.next({ action: 'removed', gameId });
          }
        });
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Get current cart item count
   */
  getItemCount(): number {
    return this.currentCart.total_items;
  }

  /**
   * Get current cart total price
   */
  getTotalPrice(): number {
    return this.currentCart.total_price;
  }

  /**
   * Check if game is in cart
   */
  isInCart(gameId: number): boolean {
    return this.currentCart.items.some(item => item.game === gameId);
  }

  /**
   * Get cart item by game ID
   */
  getCartItem(gameId: number): CartItem | undefined {
    return this.currentCart.items.find(item => item.game === gameId);
  }

  /**
   * Enrich cart items with game data and calculate totals
   */
  private enrichCartItems(apiItems: any[]): Cart {
    const items: CartItem[] = apiItems.map(apiItem => ({
      id: apiItem.id,
      user: apiItem.user,
      game: apiItem.game,
      game_title: apiItem.game_title,
      game_package: apiItem.game_package,
      game_package_name: apiItem.game_package_name,
      quantity: apiItem.quantity,
      added_at: apiItem.added_at
    }));

    // Calculate totals - each item counts as 1 since we removed quantity functionality
    const total_items = items.length;

    // For now, set total_price to 0 - we could enhance this by fetching game data
    // or including price information in the API response
    const total_price = 0;

    return {
      items,
      total_items,
      total_price
    };
  }

  /**
   * Handle HTTP errors
   */
  private handleError = (error: HttpErrorResponse): Observable<never> => {
    let errorMessage = 'An error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = error.error.message;
    } else {
      // Server-side error
      if (error.status === 401) {
        errorMessage = 'Unauthorized. Please log in.';
      } else if (error.status === 400 && error.error) {
        // Handle validation errors
        const cartError = error.error as CartError;
        if (cartError.non_field_errors) {
          errorMessage = cartError.non_field_errors[0];
        } else if (cartError.game) {
          errorMessage = cartError.game[0];
        }
      } else {
        errorMessage = `Error: ${error.status} - ${error.message}`;
      }
    }

    console.error('Cart service error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  };
}
